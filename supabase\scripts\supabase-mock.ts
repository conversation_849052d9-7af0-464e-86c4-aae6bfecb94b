/**
 * Supabase Mock Data Script
 *
 * This script creates mock users for local development and testing.
 * It creates three types of users with realistic mock data:
 * - 1 admin user with admin role
 * - 1 provider user with provider role
 * - 1 customer user with customer role (automatically assigned by trigger)
 *
 * The script ensures users are not duplicated by checking existing emails.
 *
 * Usage:
 *   pnpm supabase:mock
 *
 * Requirements:
 *   - Local Supabase instance must be running
 *   - Environment variables must be configured (.env file)
 *   - Database migrations must be applied
 */

import { serviceClient } from "../lib/service-client";
import { randomUUID } from "node:crypto";

// Mock user data configuration
const MOCK_USERS = [
  {
    role: "admin",
    email: "<EMAIL>",
    username: "testo",
    nickname: "<PERSON><PERSON>",
    bio: "",
    soda_balance: 10000,
    cap_balance: 1000
  },
  {
    role: "provider",
    email: "<EMAIL>",
    username: "provider-user",
    nickname: "Provider User",
    bio: "Service provider offering various activities",
    soda_balance: 5000,
    cap_balance: 500
  },
  {
    role: "customer",
    email: "<EMAIL>",
    username: "customer-user",
    nickname: "Customer User",
    bio: "Regular customer looking for fun activities",
    soda_balance: 2000,
    cap_balance: 100
  }
] as const;

/**
 * Check if a user with the given email already exists
 */
async function userExists(email: string): Promise<boolean> {
  try {
    const { data } = await serviceClient.auth.admin.listUsers();
    return data.users.some((user) => user.email === email);
  } catch (error) {
    console.error(`❌ Error checking if user exists: ${email}`, error);
    return false;
  }
}

/**
 * Create a single mock user with profile and wallet
 */
async function createMockUser(userData: (typeof MOCK_USERS)[number]) {
  const { role, email, username, nickname, bio, soda_balance, cap_balance } =
    userData;

  console.log(`\n📝 Creating ${role} user: ${email}`);

  try {
    // Check if user already exists
    if (await userExists(email)) {
      console.log(`⚠️  User ${email} already exists, skipping...`);
      return;
    }

    // Create user in auth.users
    const userCreation = await serviceClient.auth.admin.createUser({
      id: randomUUID(),
      email,
      password: "password123",
      email_confirm: true
    });

    if (!userCreation.data.user) {
      throw new Error(`Failed to create ${role} user`);
    }

    const userId = userCreation.data.user.id;
    console.log(`✅ Created auth user: ${userId}`);

    // Assign role (skip for customer as it's auto-assigned by trigger)
    if (role !== "customer") {
      await serviceClient.schema("app_access").rpc("assign_role_to_user", {
        v_user_id: userId,
        v_role_name: role
      });
      console.log(`✅ Assigned ${role} role`);
    }

    // Create profile with retry logic for unique username
    let retryCount = 0;
    const maxRetries = 3;
    let finalUsername: string = username;

    while (retryCount < maxRetries) {
      try {
        await serviceClient.schema("app_account").from("profile").insert({
          user_id: userId,
          username: finalUsername,
          nickname,
          bio
        });

        console.log(`✅ Created profile with username: ${finalUsername}`);
        break;
      } catch (error: unknown) {
        if (
          typeof error === "object" &&
          error !== null &&
          "code" in error &&
          (error as { code: string }).code === "23505" &&
          retryCount < maxRetries - 1
        ) {
          // Username conflict, try with timestamp suffix
          retryCount++;
          const timestamp = Date.now().toString(36);
          finalUsername = `${username}-${timestamp}`;
          console.log(`⚠️  Username conflict, retrying with: ${finalUsername}`);
        } else {
          throw error;
        }
      }
    }

    // Create wallet
    await serviceClient.schema("app_transaction").from("wallet").insert({
      user_id: userId,
      soda_balance,
      cap_balance
    });
    console.log(
      `✅ Created wallet with ${soda_balance} soda, ${cap_balance} cap`
    );

    // Additional setup for provider
    if (role === "provider") {
      // Add to approved users
      await serviceClient
        .schema("app_provider")
        .from("approved_user")
        .insert({ user_id: userId });
      console.log(`✅ Added to approved providers`);

      // Set provider status as open for orders
      await serviceClient.schema("app_provider").from("status").insert({
        user_id: userId,
        is_open_for_orders: true
      });
      console.log(`✅ Set provider status as open for orders`);
    }

    console.log(`🎉 Successfully created ${role} user: ${email}`);
  } catch (error) {
    console.error(`❌ Error creating ${role} user:`, error);
    throw error;
  }
}

/**
 * Main function to create all mock users
 */
async function main() {
  console.log("🚀 Starting mock data creation for E-Senpai...\n");

  try {
    // Create all mock users
    for (const userData of MOCK_USERS) {
      await createMockUser(userData);
    }

    console.log("\n🎉 Mock data creation completed successfully!");
    console.log("\n📋 Created users:");
    console.log("   • <EMAIL> (password: password123)");
    console.log("   • <EMAIL> (password: password123)");
    console.log("   • <EMAIL> (password: password123)");
    console.log(
      "\n💡 You can now use these accounts for local development and testing."
    );
  } catch (error) {
    console.error("\n❌ Failed to create mock data:", error);
    process.exit(1);
  }
}

// Run the script
main();
